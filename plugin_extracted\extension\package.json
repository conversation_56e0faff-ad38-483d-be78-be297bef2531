{"name": "mobile-forwarder-004", "displayName": "📱 手机转发004 - 项老师专用版", "description": "项老师专用手机消息转发器004，支持手机到VSCode Augment的无缝消息转发", "version": "1.0.0", "publisher": "rescue-team", "engines": {"vscode": "^1.82.0"}, "categories": ["Other", "AI", "Productivity"], "keywords": ["救命", "远程编程", "手机消息", "Augment转发", "防鳄鱼"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "rescueForwarder.connect", "title": "🔗 连接救命服务器", "category": "救命转发器"}, {"command": "rescueForwarder.disconnect", "title": "🔌 断开连接", "category": "救命转发器"}, {"command": "rescueForwarder.status", "title": "📊 查看救命状态", "category": "救命转发器"}, {"command": "rescueForwarder.testForward", "title": "🧪 测试消息转发", "category": "救命转发器"}, {"command": "rescueForwarder.testActivateWindow", "title": "🔥 测试窗口激活", "category": "救命转发器"}, {"command": "rescueForwarder.emergencyStop", "title": "🚨 紧急停止（老板来了）", "category": "救命转发器"}], "configuration": {"title": "救命转发器设置", "properties": {"rescueForwarder.enabled": {"type": "boolean", "default": true, "description": "启用救命功能"}, "rescueForwarder.deviceId": {"type": "string", "default": "拯救程序员的电脑004", "description": "设备标识"}, "rescueForwarder.pairId": {"type": "string", "default": "GROUP_004", "description": "救命组ID"}, "rescueForwarder.userId": {"type": "string", "default": "即将被喂鳄鱼的程序员", "description": "用户标识"}, "rescueForwarder.serverUrl": {"type": "string", "default": "wss://augment.diwangzhidao.com/ws", "description": "救命服务器地址"}, "rescueForwarder.autoConnect": {"type": "boolean", "default": true, "description": "启动时自动连接（救命要快）"}, "rescueForwarder.stealthMode": {"type": "boolean", "default": false, "description": "隐身模式（老板在附近时启用）"}, "rescueForwarder.forwardDelay": {"type": "number", "default": 1500, "description": "消息转发延迟（毫秒）"}, "rescueForwarder.debugLogs": {"type": "boolean", "default": true, "description": "显示调试日志"}, "rescueForwarder.activateWindow": {"type": "boolean", "default": true, "description": "🔥 收到消息时自动激活VS Code窗口并置于前台"}}}}, "dependencies": {"ftp": "^0.3.10"}}