# Test SendKeys functionality
Write-Host "Testing SendKeys methods..." -ForegroundColor Green

# Method 1: Direct PowerShell SendKeys
Write-Host "`nMethod 1 - Direct PowerShell SendKeys" -ForegroundColor Yellow
try {
    Add-Type -AssemblyName System.Windows.Forms
    [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')
    Write-Host "Method 1 SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "Method 1 FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 1

# Method 2: PowerShell command line
Write-Host "`nMethod 2 - PowerShell command line" -ForegroundColor Yellow
try {
    $result = powershell.exe -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')"
    Write-Host "Method 2 SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "Method 2 FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 1

# Method 3: VBScript
Write-Host "`nMethod 3 - VBScript" -ForegroundColor Yellow
try {
    $vbsScript = @"
Set WshShell = WScript.CreateObject("WScript.Shell")
WshShell.SendKeys "{ENTER}"
"@
    $vbsScript | Out-File -FilePath "temp_sendkeys.vbs" -Encoding ASCII
    cscript //nologo temp_sendkeys.vbs
    Remove-Item "temp_sendkeys.vbs" -Force
    Write-Host "Method 3 SUCCESS" -ForegroundColor Green
} catch {
    Write-Host "Method 3 FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Cyan
Write-Host "Execution Policy: $(Get-ExecutionPolicy)" -ForegroundColor Cyan

pause
