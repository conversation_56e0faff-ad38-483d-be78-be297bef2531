const vscode = require('vscode');

// 🆘 救命消息转发器 - 核心类
class RescueMessageForwarder {
    constructor() {
        this.ws = null;
        this.config = {};
        this.isConnected = false;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.emergencyMode = false;
        
        console.log('📱 手机转发004初始化完成 - 项老师专用');
    }

    // 📖 读取配置
    loadConfig() {
        const config = vscode.workspace.getConfiguration('rescueForwarder');
        this.config = {
            enabled: config.get('enabled', true),
            deviceId: config.get('deviceId', '项老师苹果电脑004'),
            pairId: config.get('pairId', 'GROUP_004'),
            userId: config.get('userId', '项老师004'),
            serverUrl: config.get('serverUrl', 'wss://augment.diwangzhidao.com/ws'),
            autoConnect: config.get('autoConnect', true),
            stealthMode: config.get('stealthMode', false),
            forwardDelay: config.get('forwardDelay', 100),
            debugLogs: config.get('debugLogs', true),
            activateWindow: config.get('activateWindow', true) // 🔥 新增：是否激活窗口
        };
        
        if (this.config.debugLogs) {
            console.log('📋 救命配置加载完成:', this.config);
        }
    }

    // 🔗 连接到救命服务器
    async connect() {
        if (!this.config.enabled) {
            this.showMessage('❌ 救命功能已禁用', 'error');
            return;
        }

        if (this.isConnected) {
            this.showMessage('✅ 已经连接到救命服务器', 'info');
            return;
        }

        try {
            this.showMessage('🔗 正在连接救命服务器...', 'info');
            
            // 使用Node.js的ws库（如果可用）
            let WebSocket;
            try {
                WebSocket = require('ws');
            } catch (error) {
                // 降级到浏览器WebSocket（在VSCode环境中可能不可用）
                WebSocket = global.WebSocket || require('websocket').w3cwebsocket;
            }

            this.ws = new WebSocket(this.config.serverUrl);

            this.ws.onopen = () => {
                this.isConnected = true;
                this.showMessage('🎉 救命服务器连接成功！', 'info');
                this.registerDevice();
                this.startHeartbeat();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.ws.onclose = () => {
                this.isConnected = false;
                this.showMessage('🔌 与救命服务器断开连接', 'warning');
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                this.showMessage(`❌ 救命服务器连接错误: ${error.message}`, 'error');
                this.scheduleReconnect();
            };

        } catch (error) {
            this.showMessage(`❌ 连接失败: ${error.message}`, 'error');
            this.scheduleReconnect();
        }
    }

    // 📝 注册设备
    registerDevice() {
        const registerMessage = {
            type: 'register',
            deviceId: this.config.deviceId,
            deviceType: 'computer',
            pairId: this.config.pairId,
            userId: this.config.userId,
            groupDisplayName: '救命组'
        };

        this.sendMessage(registerMessage);
        
        if (this.config.debugLogs) {
            console.log('📝 设备注册消息已发送:', registerMessage);
        }
    }

    // 💓 心跳机制
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                const heartbeat = {
                    type: 'heartbeat',
                    timestamp: Date.now(),
                    deviceId: this.config.deviceId
                };
                this.sendMessage(heartbeat);
            }
        }, 15000); // 15秒心跳
    }

    // 📨 处理接收到的消息
    async handleMessage(data) {
        try {
            const message = JSON.parse(data);
            
            if (this.config.debugLogs) {
                console.log('📨 收到消息:', message);
            }

            switch (message.type) {
                case 'user_message':
                    await this.handleUserMessage(message.data);
                    break;
                case 'welcome':
                    this.showMessage('🎉 欢迎使用救命转发器！', 'info');
                    break;
                default:
                    if (this.config.debugLogs) {
                        console.log('❓ 未知消息类型:', message.type);
                    }
            }
        } catch (error) {
            console.error('❌ 消息处理错误:', error);
        }
    }

    // 🚀 处理用户消息（核心功能）
    async handleUserMessage(data) {
        const { message, fromDevice, fromGroup, groupDisplayName } = data;

        // 🔥 激活VS Code窗口并置于前台（新功能）
        if (this.config.activateWindow) {
            await this.activateVSCodeWindow();
        }

        this.showMessage(`📱 收到来自 ${groupDisplayName} 的救命消息`, 'info');

        // 发送"助理查收"反馈
        await this.sendAssistantFeedback(message);

        // 延迟后转发消息到Augment
        setTimeout(async () => {
            await this.forwardToAugment(message);
        }, this.config.forwardDelay);
    }

    // 🤖 发送助理查收反馈
    async sendAssistantFeedback(message) {
        const feedback = {
            type: 'ai_status',
            data: {
                status: 'connecting',
                message: `✨ 助理查收: ${message}`,
                timestamp: Date.now()
            }
        };

        this.sendMessage(feedback);

        if (this.config.debugLogs) {
            console.log('🤖 助理查收反馈已发送');
        }
    }

    // 📤 发送状态更新
    async sendStatusUpdate(message) {
        const statusUpdate = {
            type: 'ai_status',
            data: {
                message: message,
                timestamp: Date.now()
            }
        };

        this.sendMessage(statusUpdate);

        if (this.config.debugLogs) {
            console.log(`📤 状态更新: ${message}`);
        }
    }

    // 🎯 转发消息到Augment（核心转发逻辑）
    async forwardToAugment(message) {
        try {
            this.showMessage('🎯 正在转发消息到Augment...', 'info');

            // 步骤1: 连接助理
            await this.sendStatusUpdate('🔄 正在连接项老师超级总裁助理004...');
            await this.delay(800);

            // 步骤2: 输入指令
            await this.sendStatusUpdate('⚡ 正在输入指令...');
            await this.delay(600);

            // 步骤3: 指令已发送
            await this.sendStatusUpdate('✅ 指令已发送，助理004正在处理');
            await this.delay(500);

            // 步骤4: 执行任务
            await this.sendStatusUpdate('🔄 助手004正在执行任务...');
            await this.delay(400);

            // 步骤5: 任务进行中
            await this.sendStatusUpdate('✅ 任务进行中...');
            await this.delay(300);

            // 尝试真正有效的方案
            let success = false;

            // 方案A：直接WebView消息注入（最有希望）
            success = await this.tryDirectWebViewInjection(message);

            if (!success) {
                // 方案B：通过Augment扩展API
                success = await this.tryAugmentExtensionAPI(message);
            }

            if (!success) {
                // 方案C：全局对象注入
                success = await this.tryGlobalObjectInjection(message);
            }

            if (success) {
                // 发送完成状态
                await this.sendStatusUpdate('✅ 任务已完成，请查收');
                this.showMessage('✅ 消息转发完成！AI真的开始工作了！', 'info');
            } else {
                throw new Error('所有真实转发方案都失败了，需要更深入的研究');
            }

        } catch (error) {
            this.showMessage(`❌ 转发失败: ${error.message}`, 'error');

            const errorStatus = {
                type: 'ai_status',
                data: {
                    status: 'error',
                    message: `❌ 转发失败: ${error.message}`,
                    timestamp: Date.now()
                }
            };
            this.sendMessage(errorStatus);
        }
    }

    // 🎯 方案A：直接WebView消息注入（最有希望）
    async tryDirectWebViewInjection(message) {
        try {
            if (this.config.debugLogs) {
                console.log('🎯 尝试直接WebView消息注入...');
            }

            // 1. 获取Augment扩展
            const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
            if (!augmentExtension) {
                throw new Error('Augment扩展未安装');
            }

            if (!augmentExtension.isActive) {
                await augmentExtension.activate();
                await this.delay(2000);
            }

            // 2. 尝试获取Augment的WebView实例
            // 这需要深入研究Augment的内部结构
            const augmentAPI = augmentExtension.exports;
            if (this.config.debugLogs) {
                console.log('Augment API:', augmentAPI);
            }

            // 3. 构造sendSilentExchange格式的消息
            const augmentMessage = {
                request_message: message,
                model_id: undefined, // 使用默认模型
                disableSelectedCodeDetails: false,
                chatHistory: []
            };

            // 4. 尝试直接调用（如果API可用）
            if (augmentAPI && augmentAPI.sendMessage) {
                await augmentAPI.sendMessage(augmentMessage);
                if (this.config.debugLogs) {
                    console.log('✅ 通过Augment API发送成功');
                }
                return true;
            }

            // 5. 尝试通过WebView postMessage
            if (augmentAPI && augmentAPI.webview) {
                augmentAPI.webview.postMessage({
                    type: 'chat-user-message',
                    data: augmentMessage
                });
                if (this.config.debugLogs) {
                    console.log('✅ 通过WebView postMessage发送成功');
                }
                return true;
            }

            throw new Error('无法找到Augment的消息发送接口');

        } catch (error) {
            if (this.config.debugLogs) {
                console.log('❌ 直接WebView注入失败:', error.message);
            }
            return false;
        }
    }

    // 🔌 方案B：通过Augment扩展API
    async tryAugmentExtensionAPI(message) {
        try {
            if (this.config.debugLogs) {
                console.log('🔌 尝试Augment扩展API方案...');
            }

            // 1. 智能输入框定位和焦点确保
            await this.ensureAugmentInputFocus();

            if (this.config.debugLogs) {
                console.log('✅ Augment面板焦点设置完成');
            }

            // 2. 尝试通过命令发送消息
            // 查看是否有直接发送消息的命令
            const commands = await vscode.commands.getCommands();
            const augmentCommands = commands.filter(cmd => cmd.startsWith('vscode-augment.'));

            if (this.config.debugLogs) {
                console.log('可用的Augment命令:', augmentCommands);
            }

            // 3. 尝试直接通过剪贴板发送消息到Augment聊天框
            // 将消息复制到剪贴板
            await vscode.env.clipboard.writeText(message);
            await this.delay(100);

            // 尝试粘贴到Augment聊天框
            try {
                // 方案1: 先尝试直接通过Augment API发送 (跳过粘贴+回车)
                const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
                if (augmentExtension && augmentExtension.isActive) {
                    try {
                        const augmentAPI = augmentExtension.exports;
                        if (augmentAPI && typeof augmentAPI.sendMessage === 'function') {
                            await augmentAPI.sendMessage({
                                request_message: message,
                                model_id: undefined,
                                disableSelectedCodeDetails: false,
                                chatHistory: []
                            });
                            if (this.config.debugLogs) {
                                console.log('✅ 通过Augment API直接发送成功，跳过粘贴+回车');
                            }
                            return true; // 直接成功，不需要粘贴+回车
                        }
                    } catch (apiError) {
                        if (this.config.debugLogs) {
                            console.log('❌ Augment API发送失败，降级到粘贴+回车:', apiError.message);
                        }
                    }
                }

                // 方案2: 降级到粘贴+回车方式
                await vscode.commands.executeCommand('editor.action.clipboardPasteAction');
                if (this.config.debugLogs) {
                    console.log('📋 粘贴命令已执行');
                }

                // 等待粘贴完成
                await this.delay(200);

                // 再次确保焦点在Augment面板
                await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
                await this.delay(100);

                if (this.config.debugLogs) {
                    console.log('🎯 开始尝试发送回车...');
                }

                // 发送系统级回车 (已验证有效)
                let sendSuccess = false;

                if (this.config.debugLogs) {
                    console.log('⌨️ 发送系统级回车...');
                }

                try {
                    await this.sendSystemEnter();
                    sendSuccess = true;
                    if (this.config.debugLogs) {
                        console.log('✅ 系统级回车发送成功');
                    }
                } catch (systemError) {
                    if (this.config.debugLogs) {
                        console.log('❌ 系统级回车失败:', systemError.message);
                    }
                }

                if (sendSuccess) {
                    if (this.config.debugLogs) {
                        console.log('✅ 消息已粘贴到Augment聊天框并自动发送');
                    }
                    vscode.window.showInformationMessage('🎉 消息已自动发送到Augment！');
                } else {
                    // 系统回车失败，提示用户检查权限或手动操作
                    vscode.window.showWarningMessage(
                        '❌ 系统回车发送失败！\n\n💡 请检查：\n1. VSCode是否有辅助功能权限\n2. 或手动在Augment聊天框按回车键',
                        '检查权限',
                        '我知道了'
                    ).then(selection => {
                        if (selection === '检查权限') {
                            vscode.window.showInformationMessage(
                                '🔧 权限设置：\nmacOS: 系统偏好设置 > 安全性与隐私 > 辅助功能 > 添加VSCode\nWindows: 确保PowerShell执行策略允许\nLinux: 安装xdotool'
                            );
                        }
                    });

                    if (this.config.debugLogs) {
                        console.log('⚠️ 系统回车失败，需要检查权限或手动操作');
                    }
                }

            } catch (pasteError) {
                if (this.config.debugLogs) {
                    console.log('❌ 剪贴板粘贴失败，尝试其他方式:', pasteError.message);
                }

                // 降级方案：创建临时文件但不调用explain命令
                const tempDoc = await vscode.workspace.openTextDocument({
                    content: `请帮我处理这个任务：\n\n${message}`,
                    language: 'markdown'
                });

                const editor = await vscode.window.showTextDocument(tempDoc);
                await this.delay(500);

                // 选中消息内容
                const lines = tempDoc.getText().split('\n');
                const messageStartLine = 2; // "请帮我处理这个任务："后面的空行
                const messageEndLine = lines.length - 1;

                const startPos = new vscode.Position(messageStartLine, 0);
                const endPos = new vscode.Position(messageEndLine, lines[messageEndLine].length);
                editor.selection = new vscode.Selection(startPos, endPos);
                await this.delay(300);

                // 不调用explain命令，而是提示用户手动操作
                vscode.window.showInformationMessage(
                    '📝 请手动复制选中的内容到Augment聊天框，然后按回车发送',
                    '知道了'
                );
            }

            if (this.config.debugLogs) {
                console.log('✅ Augment扩展API方案执行完成');
            }

            return true;

        } catch (error) {
            if (this.config.debugLogs) {
                console.log('❌ Augment扩展API方案失败:', error.message);
            }
            return false;
        }
    }

    // 🌐 方案C：全局对象注入
    async tryGlobalObjectInjection(message) {
        try {
            if (this.config.debugLogs) {
                console.log('🌐 尝试全局对象注入方案...');
            }

            // 1. 打开Augment面板
            await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
            await this.delay(2000);

            // 2. 尝试通过VSCode的webview API注入脚本
            // 这需要找到Augment的webview实例

            // 3. 构造JavaScript代码来调用sendSilentExchange
            const injectionScript = `
                (function() {
                    try {
                        // 尝试找到Augment的聊天模型实例
                        if (window._chatModel && window._chatModel.currentConversationModel) {
                            window._chatModel.currentConversationModel.sendSilentExchange({
                                request_message: "${message.replace(/"/g, '\\"')}",
                                model_id: undefined,
                                disableSelectedCodeDetails: false,
                                chatHistory: []
                            });
                            console.log('✅ 通过全局对象注入成功');
                            return true;
                        }

                        // 尝试其他可能的全局对象
                        if (window.He && window.He.postMessage) {
                            window.He.postMessage({
                                type: 'chat-user-message',
                                data: {
                                    request_message: "${message.replace(/"/g, '\\"')}",
                                    model_id: undefined,
                                    disableSelectedCodeDetails: false,
                                    chatHistory: []
                                }
                            });
                            console.log('✅ 通过He.postMessage注入成功');
                            return true;
                        }

                        console.log('❌ 未找到可用的全局对象');
                        return false;
                    } catch (error) {
                        console.error('❌ 全局对象注入失败:', error);
                        return false;
                    }
                })();
            `;

            // 4. 尝试执行注入脚本（这需要特殊的权限）
            // 由于安全限制，这可能不会成功
            if (this.config.debugLogs) {
                console.log('注入脚本:', injectionScript);
            }

            // 5. 降级到文件方式触发
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (workspaceFolders && workspaceFolders.length > 0) {
                const scriptPath = vscode.Uri.joinPath(workspaceFolders[0].uri, '.rescue-injection.js');
                await vscode.workspace.fs.writeFile(scriptPath, Buffer.from(injectionScript, 'utf8'));

                // 打开脚本文件
                const doc = await vscode.workspace.openTextDocument(scriptPath);
                await vscode.window.showTextDocument(doc);

                if (this.config.debugLogs) {
                    console.log('✅ 注入脚本已创建，需要手动执行');
                }
            }

            return false; // 这个方案目前无法自动执行

        } catch (error) {
            if (this.config.debugLogs) {
                console.log('❌ 全局对象注入失败:', error.message);
            }
            return false;
        }
    }


    // ⌨️ 系统级回车发送 - 修复版
    async sendSystemEnter() {
        const os = require('os');
        const { exec, spawn } = require('child_process');

        return new Promise((resolve, reject) => {
            let command;
            let helpMessage = '';

            switch (os.platform()) {
                case 'darwin': // macOS
                    command = 'osascript -e "tell application \\"System Events\\" to keystroke return"';
                    helpMessage = '如果失败，请在系统偏好设置 > 安全性与隐私 > 辅助功能中允许VSCode或终端访问';
                    break;
                case 'win32': // Windows
                    // 修复Windows命令 - 使用更稳定的方式
                    this.sendWindowsEnter().then(resolve).catch(reject);
                    return;
                case 'linux': // Linux
                    command = 'xdotool key Return';
                    helpMessage = '如果失败，请安装xdotool: sudo apt-get install xdotool 或 sudo yum install xdotool';
                    break;
                default:
                    reject(new Error(`不支持的操作系统: ${os.platform()}`));
                    return;
            }

            if (this.config.debugLogs) {
                console.log(`🖥️ 执行系统命令: ${command}`);
                console.log(`💡 提示: ${helpMessage}`);
            }

            exec(command, { timeout: 5000 }, (error, stdout, stderr) => {
                if (error) {
                    const errorMsg = `系统回车命令失败: ${error.message}\n💡 ${helpMessage}`;
                    if (this.config.debugLogs) {
                        console.log(`❌ ${errorMsg}`);
                        if (stderr) console.log(`stderr: ${stderr}`);
                    }
                    reject(new Error(errorMsg));
                } else {
                    if (this.config.debugLogs) {
                        console.log(`✅ 系统回车命令执行成功`);
                        if (stdout) console.log(`stdout: ${stdout}`);
                    }
                    resolve(stdout);
                }
            });
        });
    }

    // 🪟 Windows专用回车发送方法 - 修复版
    async sendWindowsEnter() {
        const { exec, spawn } = require('child_process');
        const fs = require('fs');
        const path = require('path');

        return new Promise((resolve, reject) => {
            // 方案1: 使用临时VBS文件 (最稳定的方案)
            const method1 = () => {
                try {
                    const tempDir = require('os').tmpdir();
                    const vbsPath = path.join(tempDir, `sendkeys_${Date.now()}.vbs`);
                    const vbsContent = 'Set WshShell = WScript.CreateObject("WScript.Shell")\nWshShell.SendKeys "{ENTER}"';

                    if (this.config.debugLogs) {
                        console.log(`🪟 方案1 - 临时VBS文件: ${vbsPath}`);
                    }

                    fs.writeFileSync(vbsPath, vbsContent, 'utf8');

                    exec(`cscript //nologo "${vbsPath}"`, { timeout: 3000 }, (error, stdout, stderr) => {
                        // 清理临时文件
                        try {
                            fs.unlinkSync(vbsPath);
                        } catch (cleanupError) {
                            // 忽略清理错误
                        }

                        if (error) {
                            if (this.config.debugLogs) {
                                console.log(`❌ 方案1失败: ${error.message}`);
                            }
                            method2(); // 尝试方案2
                        } else {
                            if (this.config.debugLogs) {
                                console.log(`✅ 方案1成功`);
                            }
                            resolve(stdout);
                        }
                    });
                } catch (fileError) {
                    if (this.config.debugLogs) {
                        console.log(`❌ 方案1文件操作失败: ${fileError.message}`);
                    }
                    method2(); // 尝试方案2
                }
            };

            // 方案2: 直接使用PowerShell SendKeys (修复转义问题)
            const method2 = () => {
                const command = 'powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'{ENTER}\')"';

                if (this.config.debugLogs) {
                    console.log(`🪟 方案2 - PowerShell SendKeys: ${command}`);
                }

                exec(command, { timeout: 3000 }, (error, stdout, stderr) => {
                    if (error) {
                        if (this.config.debugLogs) {
                            console.log(`❌ 方案2失败: ${error.message}`);
                        }
                        method3(); // 尝试方案3
                    } else {
                        if (this.config.debugLogs) {
                            console.log(`✅ 方案2成功`);
                        }
                        resolve(stdout);
                    }
                });
            };

            // 方案3: 使用spawn方式执行PowerShell
            const method3 = () => {
                if (this.config.debugLogs) {
                    console.log(`🪟 方案3 - Spawn PowerShell`);
                }

                const ps = spawn('powershell.exe', [
                    '-WindowStyle', 'Hidden',
                    '-ExecutionPolicy', 'Bypass',
                    '-Command',
                    'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'{ENTER}\')'
                ], { timeout: 3000 });

                let output = '';
                let errorOutput = '';

                ps.stdout.on('data', (data) => {
                    output += data.toString();
                });

                ps.stderr.on('data', (data) => {
                    errorOutput += data.toString();
                });

                ps.on('close', (code) => {
                    if (code === 0) {
                        if (this.config.debugLogs) {
                            console.log(`✅ 方案3成功`);
                        }
                        resolve(output);
                    } else {
                        if (this.config.debugLogs) {
                            console.log(`❌ 方案3失败，退出码: ${code}, 错误: ${errorOutput}`);
                        }
                        method4(); // 尝试方案4
                    }
                });

                ps.on('error', (error) => {
                    if (this.config.debugLogs) {
                        console.log(`❌ 方案3进程错误: ${error.message}`);
                    }
                    method4(); // 尝试方案4
                });
            };

            // 方案4: 使用echo管道方式的VBScript
            const method4 = () => {
                if (this.config.debugLogs) {
                    console.log(`🪟 方案4 - Echo管道VBScript`);
                }

                const vbsCommand = 'echo Set WshShell = WScript.CreateObject("WScript.Shell") : WshShell.SendKeys "{ENTER}" | cscript //nologo';

                exec(vbsCommand, { timeout: 3000 }, (error, stdout, stderr) => {
                    if (error) {
                        if (this.config.debugLogs) {
                            console.log(`❌ 方案4失败: ${error.message}`);
                        }
                        method5(); // 尝试方案5
                    } else {
                        if (this.config.debugLogs) {
                            console.log(`✅ 方案4成功`);
                        }
                        resolve(stdout);
                    }
                });
            };

            // 方案5: 使用cmd方式 (最后的备用方案)
            const method5 = () => {
                if (this.config.debugLogs) {
                    console.log(`🪟 方案5 - CMD方式 (最后尝试)`);
                }

                const cmdCommand = 'cmd /c "echo. | powershell -ExecutionPolicy Bypass -Command \\"Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\\\'{ENTER}\\\')\\\""';

                exec(cmdCommand, { timeout: 5000 }, (error, stdout, stderr) => {
                    if (error) {
                        if (this.config.debugLogs) {
                            console.log(`❌ 方案5失败: ${error.message}`);
                            console.log(`stderr: ${stderr}`);
                        }
                        reject(new Error(`所有Windows回车发送方案都失败了。\n\n请检查：\n1. PowerShell执行策略 (当前: ${process.env.PSExecutionPolicyPreference || '未设置'})\n2. 系统权限设置\n3. 防病毒软件是否阻止脚本执行\n4. Windows版本兼容性\n\n建议手动测试: powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')"`));
                    } else {
                        if (this.config.debugLogs) {
                            console.log(`✅ 方案5成功`);
                        }
                        resolve(stdout);
                    }
                });
            };

            // 开始尝试方案1 (临时VBS文件 - 最稳定)
            method1();
        });
    }

    // 🔥 激活VS Code窗口并置于前台
    async activateVSCodeWindow() {
        const os = require('os');
        const { exec } = require('child_process');

        return new Promise((resolve) => {
            let command;
            let helpMessage = '';

            switch (os.platform()) {
                case 'darwin': // macOS
                    command = 'osascript -e \'tell application "Visual Studio Code" to activate\'';
                    helpMessage = '如果失败，请确保VS Code在应用程序文件夹中，或尝试使用"Code"作为应用程序名称';
                    break;
                case 'win32': // Windows
                    // 尝试多种方式激活窗口
                    command = 'powershell -Command "' +
                        'Add-Type -AssemblyName Microsoft.VisualBasic; ' +
                        'try { [Microsoft.VisualBasic.Interaction]::AppActivate(\'Visual Studio Code\') } ' +
                        'catch { [Microsoft.VisualBasic.Interaction]::AppActivate(\'Code\') }"';
                    helpMessage = '如果失败，请确保PowerShell执行策略允许运行脚本';
                    break;
                case 'linux': // Linux
                    // 优先使用wmctrl，如果失败则尝试xdotool
                    command = 'wmctrl -a "Visual Studio Code" || wmctrl -a "Code" || ' +
                        'xdotool search --name "Visual Studio Code" windowactivate || ' +
                        'xdotool search --name "Code" windowactivate';
                    helpMessage = '如果失败，请安装wmctrl或xdotool: sudo apt-get install wmctrl xdotool';
                    break;
                default:
                    if (this.config.debugLogs) {
                        console.log(`❌ 不支持的操作系统: ${os.platform()}`);
                    }
                    resolve(); // 不支持的系统直接返回
                    return;
            }

            if (this.config.debugLogs) {
                console.log(`🔥 激活VS Code窗口: ${command}`);
                console.log(`💡 提示: ${helpMessage}`);
            }

            exec(command, { timeout: 3000 }, (error, stdout, stderr) => {
                if (error) {
                    if (this.config.debugLogs) {
                        console.log(`⚠️ 窗口激活失败: ${error.message}`);
                        console.log(`💡 ${helpMessage}`);
                        if (stderr) console.log(`stderr: ${stderr}`);
                    }
                    // 即使失败也继续执行，不阻塞主流程
                } else {
                    if (this.config.debugLogs) {
                        console.log(`✅ VS Code窗口激活成功`);
                        if (stdout) console.log(`stdout: ${stdout}`);
                    }
                }
                resolve(); // 无论成功失败都继续
            });
        });
    }

    // 🎯 智能输入框定位和焦点确保
    async ensureAugmentInputFocus() {
        if (this.config.debugLogs) {
            console.log('🎯 开始智能输入框定位...');
        }

        // 1. 确保Augment面板打开
        await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
        await this.delay(200);

        // 2. 多重焦点确保策略 (减少尝试次数)
        for (let attempt = 0; attempt < 2; attempt++) {
            if (this.config.debugLogs) {
                console.log(`🔄 焦点确保尝试 ${attempt + 1}/5`);
            }

            // 策略A: 标准面板焦点
            await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
            await this.delay(100);

            // 策略B: 尝试激活编辑器组后回到面板
            try {
                await vscode.commands.executeCommand('workbench.action.focusActiveEditorGroup');
                await this.delay(50);
                await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
                await this.delay(100);
            } catch (error) {
                if (this.config.debugLogs) {
                    console.log(`焦点策略B失败:`, error.message);
                }
            }

            // 策略C: 模拟Tab键导航到输入框
            try {
                for (let tab = 0; tab < 2; tab++) {
                    await vscode.commands.executeCommand('workbench.action.focusNextPart');
                    await this.delay(30);
                }
            } catch (error) {
                if (this.config.debugLogs) {
                    console.log(`Tab导航失败:`, error.message);
                }
            }

            // 策略D: 尝试点击操作
            try {
                await vscode.commands.executeCommand('workbench.action.focusPanel');
                await this.delay(50);
                await vscode.commands.executeCommand('vscode-augment.focusAugmentPanel');
                await this.delay(100);
            } catch (error) {
                if (this.config.debugLogs) {
                    console.log(`焦点策略D失败:`, error.message);
                }
            }
        }

        if (this.config.debugLogs) {
            console.log('✅ 输入框焦点确保完成');
        }
    }

    // 📤 发送消息到服务器
    sendMessage(message) {
        if (this.ws && this.isConnected) {
            this.ws.send(JSON.stringify(message));
        }
    }

    // ⏰ 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 💬 显示消息
    showMessage(message, type = 'info') {
        if (this.config.stealthMode) return; // 隐身模式不显示消息
        
        switch (type) {
            case 'error':
                vscode.window.showErrorMessage(message);
                break;
            case 'warning':
                vscode.window.showWarningMessage(message);
                break;
            default:
                vscode.window.showInformationMessage(message);
        }
    }

    // 🔄 重连机制
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected && this.config.enabled) {
                this.connect();
            }
        }, 5000); // 5秒后重连
    }

    // 🔌 断开连接
    disconnect() {
        this.isConnected = false;
        
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.showMessage('🔌 救命转发器已断开', 'info');
    }

    // 🚨 紧急停止
    emergencyStop() {
        this.emergencyMode = true;
        this.disconnect();
        this.showMessage('🚨 紧急停止！救命转发器已关闭', 'warning');
    }

    // 📊 获取状态
    getStatus() {
        return {
            connected: this.isConnected,
            emergency: this.emergencyMode,
            config: this.config
        };
    }
}

// 全局实例
let rescueForwarder = null;

// 🚀 插件激活
function activate(context) {
    console.log('🆘 救命消息转发器正在启动...');
    
    rescueForwarder = new RescueMessageForwarder();
    rescueForwarder.loadConfig();
    
    // 自动连接
    if (rescueForwarder.config.autoConnect) {
        rescueForwarder.connect();
    }
    
    // 注册命令
    const commands = [
        vscode.commands.registerCommand('rescueForwarder.connect', () => {
            rescueForwarder.connect();
        }),
        
        vscode.commands.registerCommand('rescueForwarder.disconnect', () => {
            rescueForwarder.disconnect();
        }),
        
        vscode.commands.registerCommand('rescueForwarder.status', () => {
            const status = rescueForwarder.getStatus();
            const statusText = `
🆘 救命转发器状态:
- 连接状态: ${status.connected ? '✅ 已连接' : '❌ 未连接'}
- 紧急模式: ${status.emergency ? '🚨 已启用' : '✅ 正常'}
- 设备ID: ${status.config.deviceId}
- 配对组: ${status.config.pairId}
            `;
            vscode.window.showInformationMessage(statusText);
        }),
        
        vscode.commands.registerCommand('rescueForwarder.testForward', async () => {
            const testMessage = '🧪 这是一条测试消息，用来验证转发功能是否正常工作';
            await rescueForwarder.forwardToAugment(testMessage);
        }),

        vscode.commands.registerCommand('rescueForwarder.testActivateWindow', async () => {
            vscode.window.showInformationMessage('🔥 正在测试窗口激活功能...');
            await rescueForwarder.activateVSCodeWindow();
            vscode.window.showInformationMessage('✅ 窗口激活测试完成！');
        }),
        
        vscode.commands.registerCommand('rescueForwarder.emergencyStop', () => {
            rescueForwarder.emergencyStop();
        })
    ];
    
    commands.forEach(command => context.subscriptions.push(command));
    
    // 显示启动消息
    vscode.window.showInformationMessage('🆘 救命消息转发器已启动！准备拯救你免于被喂鳄鱼！');
    
    console.log('🎉 救命消息转发器启动完成！');
}

// 🛑 插件停用
function deactivate() {
    if (rescueForwarder) {
        rescueForwarder.disconnect();
    }
    console.log('🛑 救命消息转发器已停用');
}

module.exports = {
    activate,
    deactivate
};
