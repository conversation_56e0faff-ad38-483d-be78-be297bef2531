// VS Code扩展环境中的SendKeys测试脚本
// 在VS Code开发者控制台中运行此脚本来诊断问题

console.log('🔍 开始VS Code扩展环境SendKeys测试...');

// 测试1: 检查Node.js child_process模块
try {
    const { exec, spawn } = require('child_process');
    console.log('✅ child_process模块加载成功');
    
    // 测试2: 执行简单的PowerShell命令
    exec('powershell -Command "Write-Host \'Test PowerShell\'"', (error, stdout, stderr) => {
        if (error) {
            console.log('❌ PowerShell基础测试失败:', error.message);
        } else {
            console.log('✅ PowerShell基础测试成功:', stdout);
            
            // 测试3: 执行SendKeys命令
            console.log('🧪 开始SendKeys测试...');
            exec('powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'{ENTER}\')"', 
                { timeout: 5000 }, 
                (error, stdout, stderr) => {
                    if (error) {
                        console.log('❌ SendKeys测试失败:');
                        console.log('  错误信息:', error.message);
                        console.log('  错误代码:', error.code);
                        console.log('  stderr:', stderr);
                    } else {
                        console.log('✅ SendKeys测试成功!');
                        console.log('  stdout:', stdout);
                    }
                }
            );
        }
    });
    
} catch (moduleError) {
    console.log('❌ child_process模块加载失败:', moduleError.message);
}

// 测试4: 检查当前进程信息
console.log('📊 当前进程信息:');
console.log('  进程ID:', process.pid);
console.log('  Node.js版本:', process.version);
console.log('  平台:', process.platform);
console.log('  架构:', process.arch);
console.log('  当前工作目录:', process.cwd());

// 测试5: 检查环境变量
console.log('🔧 相关环境变量:');
console.log('  USERPROFILE:', process.env.USERPROFILE);
console.log('  TEMP:', process.env.TEMP);
console.log('  PSExecutionPolicyPreference:', process.env.PSExecutionPolicyPreference);

// 测试6: 尝试VBScript方式
console.log('🧪 测试VBScript方式...');
const fs = require('fs');
const path = require('path');
const os = require('os');

try {
    const tempDir = os.tmpdir();
    const vbsPath = path.join(tempDir, 'test_sendkeys.vbs');
    const vbsContent = 'Set WshShell = WScript.CreateObject("WScript.Shell")\nWshShell.SendKeys "{ENTER}"';
    
    fs.writeFileSync(vbsPath, vbsContent, 'utf8');
    console.log('✅ VBS文件创建成功:', vbsPath);
    
    exec(`cscript //nologo "${vbsPath}"`, (error, stdout, stderr) => {
        // 清理临时文件
        try {
            fs.unlinkSync(vbsPath);
        } catch (cleanupError) {
            console.log('⚠️ 临时文件清理失败:', cleanupError.message);
        }
        
        if (error) {
            console.log('❌ VBScript测试失败:', error.message);
        } else {
            console.log('✅ VBScript测试成功!');
        }
    });
    
} catch (vbsError) {
    console.log('❌ VBScript测试异常:', vbsError.message);
}

console.log('🎯 测试脚本执行完成，请查看上述结果');
console.log('💡 如果所有测试都成功，说明问题可能在于:');
console.log('   1. 插件执行时机不对（焦点、时序问题）');
console.log('   2. VS Code扩展沙箱限制');
console.log('   3. 异步操作竞争条件');
