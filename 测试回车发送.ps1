# 测试回车发送功能的脚本
# 用于验证修复后的插件是否能正常发送回车键

Write-Host "🧪 开始测试回车发送功能..." -ForegroundColor Green

# 方案1: 直接PowerShell SendKeys
Write-Host "`n🪟 测试方案1 - PowerShell SendKeys" -ForegroundColor Yellow
try {
    $command1 = 'powershell.exe -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(''{ENTER}'')"'
    Write-Host "执行命令: $command1" -ForegroundColor Cyan
    Invoke-Expression $command1
    Write-Host "✅ 方案1执行成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 方案1失败: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 1

# 方案2: 使用Start-Process
Write-Host "`n🪟 测试方案2 - Start-Process方式" -ForegroundColor Yellow
try {
    $ps2 = Start-Process -FilePath "powershell.exe" -ArgumentList @(
        "-WindowStyle", "Hidden",
        "-Command", 
        "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')"
    ) -Wait -PassThru -NoNewWindow
    
    if ($ps2.ExitCode -eq 0) {
        Write-Host "✅ 方案2执行成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 方案2失败，退出码: $($ps2.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 方案2失败: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 1

# 方案3: VBScript方式
Write-Host "`n🪟 测试方案3 - VBScript SendKeys" -ForegroundColor Yellow
try {
    $vbsCommand = 'echo Set WshShell = WScript.CreateObject("WScript.Shell") : WshShell.SendKeys "{ENTER}" | cscript //nologo'
    Write-Host "执行命令: $vbsCommand" -ForegroundColor Cyan
    Invoke-Expression $vbsCommand
    Write-Host "✅ 方案3执行成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 方案3失败: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 1

# 方案4: 直接在当前PowerShell会话中执行
Write-Host "`n🪟 测试方案4 - 当前会话直接执行" -ForegroundColor Yellow
try {
    Add-Type -AssemblyName System.Windows.Forms
    [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')
    Write-Host "✅ 方案4执行成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 方案4失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 测试完成！请检查哪个方案能正常工作。" -ForegroundColor Green
Write-Host "💡 如果所有方案都失败，可能需要检查：" -ForegroundColor Yellow
Write-Host "   1. PowerShell执行策略" -ForegroundColor White
Write-Host "   2. 系统权限设置" -ForegroundColor White
Write-Host "   3. 防病毒软件是否阻止" -ForegroundColor White
Write-Host "   4. Windows版本兼容性" -ForegroundColor White

# 显示当前系统信息
Write-Host "`n📊 当前系统信息：" -ForegroundColor Cyan
Write-Host "PowerShell版本: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "执行策略: $(Get-ExecutionPolicy)" -ForegroundColor White
Write-Host "操作系统: $([System.Environment]::OSVersion.VersionString)" -ForegroundColor White

Read-Host "`n按回车键退出"
