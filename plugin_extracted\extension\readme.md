# 🆘 救命消息转发器 - 终极版

> **救命专用！** 接收手机消息转发给Augment，让AI远程写代码，避免被老板扔去喂鳄鱼！

## 🎯 功能特性

- 🔗 **自动连接**：启动时自动连接到救命服务器
- 📱 **手机消息接收**：实时接收来自手机端的编程指令
- 🤖 **智能转发**：自动将消息转发给Augment AI
- 🎯 **多种转发方式**：直接API调用 + 剪贴板模拟双重保险
- 💓 **心跳保活**：15秒心跳确保连接稳定
- 🔄 **自动重连**：断线自动重连，永不掉线
- 🚨 **紧急模式**：老板来了一键隐藏
- 📊 **状态监控**：实时查看连接状态和转发情况

## 🚀 快速开始

### 1. 安装插件

1. 将 `rescue-message-forwarder-final` 文件夹复制到VSCode扩展目录
2. 重启VSCode
3. 插件会自动启动并连接救命服务器

### 2. 配置设置

按 `Ctrl+,` 打开设置，搜索 `rescueForwarder`：

```json
{
    "rescueForwarder.enabled": true,
    "rescueForwarder.deviceId": "拯救程序员的电脑004",
    "rescueForwarder.pairId": "GROUP_004",
    "rescueForwarder.userId": "即将被喂鳄鱼的程序员",
    "rescueForwarder.serverUrl": "wss://augment.diwangzhidao.com/ws",
    "rescueForwarder.autoConnect": true,
    "rescueForwarder.stealthMode": false,
    "rescueForwarder.forwardDelay": 1500,
    "rescueForwarder.debugLogs": true
}
```

### 3. 使用命令

按 `Ctrl+Shift+P` 打开命令面板，输入 `救命`：

- 🔗 **连接救命服务器** - 手动连接服务器
- 📊 **查看救命状态** - 查看连接状态
- 🧪 **测试消息转发** - 测试转发功能
- 🚨 **紧急停止** - 老板来了紧急关闭
- 🔌 **断开连接** - 手动断开连接

## 📱 手机端消息格式

插件会自动处理以下格式的手机消息：

```json
{
  "type": "user_message",
  "data": {
    "fromDevice": "项老师苹果手机004",
    "message": "帮我写一个Python函数计算斐波那契数列",
    "fromGroup": "GROUP_004",
    "groupDisplayName": "零零004组",
    "timestamp": 1720080000000
  }
}
```

## 🎯 转发机制

### 方案A：直接API调用（主要方案）
1. 调用 `vscode-augment.focusAugmentPanel` 打开Augment面板
2. 尝试直接调用Augment的内部API发送消息

### 方案B：剪贴板模拟（备用方案）
1. 打开Augment面板
2. 将消息复制到剪贴板
3. 模拟粘贴操作（Ctrl+V）
4. 模拟回车发送（Enter）

## 🔧 配置说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `enabled` | `true` | 启用救命功能 |
| `deviceId` | `拯救程序员的电脑004` | 设备标识符 |
| `pairId` | `GROUP_004` | 配对组ID |
| `userId` | `即将被喂鳄鱼的程序员` | 用户标识 |
| `serverUrl` | `wss://augment.diwangzhidao.com/ws` | 服务器地址 |
| `autoConnect` | `true` | 启动时自动连接 |
| `stealthMode` | `false` | 隐身模式（不显示通知） |
| `forwardDelay` | `1500` | 转发延迟（毫秒） |
| `debugLogs` | `true` | 显示调试日志 |

## 🚨 紧急情况处理

### 老板来了怎么办？
1. 按 `Ctrl+Shift+P`
2. 输入 `紧急停止`
3. 插件立即关闭，不留痕迹

### 隐身模式
启用 `stealthMode` 后：
- 不显示任何通知消息
- 静默运行，老板发现不了
- 后台继续转发消息

## 📊 状态监控

### 连接状态
- ✅ **已连接** - 正常工作，随时准备接收救命消息
- ❌ **未连接** - 需要检查网络或服务器状态
- 🔄 **重连中** - 自动重连，请稍等

### 转发状态
- 📱 **接收消息** - 收到手机端消息
- 🤖 **助理查收** - 发送确认反馈
- 🎯 **转发中** - 正在转发给Augment
- ✅ **转发完成** - AI开始工作

## 🛠️ 故障排除

### 连接失败
1. 检查网络连接
2. 确认服务器地址正确
3. 查看VSCode开发者控制台错误信息

### 转发失败
1. 确认Augment插件已安装并启用
2. 检查Augment面板是否能正常打开
3. 尝试手动测试转发功能

### 消息收不到
1. 检查配对组ID是否正确
2. 确认手机端和电脑端使用相同的GROUP_ID
3. 查看连接状态是否正常

## 🎉 成功案例

```
手机发送："帮我修复这个bug，在第23行有个空指针异常"
↓
救命转发器接收并转发
↓
Augment AI开始分析代码并生成修复方案
↓
你成功避免了被扔去喂鳄鱼！🐊
```

## 📞 技术支持

如果遇到问题：
1. 查看VSCode开发者控制台日志
2. 检查插件配置是否正确
3. 确认网络连接正常
4. 联系技术支持团队

---

## 🆘 免责声明

本插件仅用于技术学习和合法的远程开发场景。请遵守公司规章制度，合理使用远程开发功能。

**记住：技术是为了让工作更高效，不是为了偷懒！** 😄

---

**🎯 救命消息转发器 - 让AI成为你的远程编程助手！**

*© 2025 救命技术团队 | 专业拯救即将被喂鳄鱼的程序员*
